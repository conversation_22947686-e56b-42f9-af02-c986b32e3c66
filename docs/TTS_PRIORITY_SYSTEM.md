# TTS Priority System for DassoShu Reader

## Overview

The pronunciation functionality in DassoShu Reader uses a carefully ordered TTS (Text-to-Speech) fallback system optimized for global accessibility, particularly for users in China where certain services are blocked.

## TTS Service Priority Order

### 1. System TTS (Primary) 🥇
- **Service**: Flutter's built-in system TTS
- **Accessibility**: ✅ Works everywhere (including China)
- **Quality**: Good (depends on device)
- **Offline**: ✅ Works offline
- **Implementation**: Uses existing `TtsFactory` and `QuickTtsService`

### 2. Microsoft Edge TTS (Secondary) 🥈
- **Service**: Microsoft Edge Speech Services
- **Accessibility**: ✅ Works in China
- **Quality**: Excellent (neural voices)
- **Offline**: ❌ Requires internet
- **Implementation**: Multiple endpoints with fallback

### 3. Google Translate TTS (Final Fallback) 🥉
- **Service**: Google Translate TTS API
- **Accessibility**: ❌ Blocked in China (requires VPN)
- **Quality**: Good
- **Offline**: ❌ Requires internet
- **Implementation**: Traditional Google TTS endpoint

## Geographic Considerations

### China Users 🇨🇳
- **Primary**: System TTS (always works)
- **Secondary**: Edge TTS (accessible)
- **Fallback**: Google TTS (blocked without VPN)

### International Users 🌍
- **Primary**: System TTS (reliable)
- **Secondary**: Edge TTS (high quality)
- **Fallback**: Google TTS (widely available)

## Implementation Details

### Service Selection Logic
```dart
// 1. Try System TTS first (universal accessibility)
if (await _trySystemTtsFallback(cleanText, context)) {
  return true;
}

// 2. Try Edge TTS (China-accessible)
if (await _tryEdgeTts(cleanText)) {
  return true;
}

// 3. Try Google TTS (final fallback)
if (await _tryGoogleTranslateTts(cleanText)) {
  return true;
}
```

### Error Handling
- Each service fails gracefully to the next
- Comprehensive logging for debugging
- User-friendly error messages
- No blocking behavior

## Benefits of This Approach

1. **Universal Accessibility**: Works for users in China without VPN
2. **Reliability**: Multiple fallback options ensure pronunciation always works
3. **Performance**: System TTS is fastest (no network required)
4. **Quality**: High-quality neural voices when available
5. **Offline Support**: System TTS works without internet

## Testing Recommendations

### For China Users
1. Test with system TTS (should work)
2. Test with Edge TTS (should work)
3. Test with VPN off (Google TTS should fail gracefully)

### For International Users
1. Test all three services
2. Test with poor network conditions
3. Test offline functionality

## Configuration

The TTS priority system is automatically configured and requires no user intervention. The system intelligently selects the best available service based on:

- Network connectivity
- Geographic location (implicit)
- Service availability
- Error conditions

## Troubleshooting

### No Sound Issues
1. Check device volume settings
2. Verify system TTS is enabled in device settings
3. Check network connectivity for online services
4. Review app logs for specific error messages

### China-Specific Issues
1. System TTS should always work
2. Edge TTS should work without VPN
3. Google TTS will fail without VPN (expected behavior)

## Future Improvements

- Add user preference for TTS service selection
- Implement voice selection for supported services
- Add pronunciation speed controls
- Consider additional China-accessible TTS services
