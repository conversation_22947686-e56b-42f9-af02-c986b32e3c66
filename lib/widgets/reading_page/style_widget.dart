import 'dart:io';

import 'package:dasso_reader/config/contrast_audit.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/models/font_model.dart';
import 'package:dasso_reader/page/settings_page/subpage/fonts.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/font.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/font_parser.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/utils/toast/common.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

// Custom thumb shape that displays text inside
class TextThumbShape extends SliderComponentShape {
  final double thumbRadius;
  final TextStyle textStyle;
  final double elevation;
  final String Function(double value)? valueToString;

  const TextThumbShape({
    required this.thumbRadius,
    required this.textStyle,
    this.elevation = DesignSystem.elevationS,
    this.valueToString,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // Draw shadow
    if (elevation > 0) {
      final shadowPath = Path()
        ..addOval(Rect.fromCircle(center: center, radius: thumbRadius));
      canvas.drawShadow(shadowPath, Colors.black, elevation, true);
    }

    // Draw the white circle
    final fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, thumbRadius, fillPaint);

    // Draw a subtle border
    final borderPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    canvas.drawCircle(center, thumbRadius, borderPaint);

    // Get the text to display - either from the valueToString callback or directly
    final text = valueToString != null
        ? valueToString!(value)
        : value.toStringAsFixed(1);

    // Draw the text
    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();

    final textOffset = Offset(
      center.dx - (textPainter.width / 2),
      center.dy - (textPainter.height / 2),
    );

    textPainter.paint(canvas, textOffset);
  }
}

// Custom track shape with icons and dots
class IconSliderTrackShape extends RoundedRectSliderTrackShape {
  final int divisions;
  final Color dotColor;
  final double dotSize;
  final double opacity;
  final IconData? leftIcon;
  final IconData? rightIcon;
  final String? leftText;
  final String? rightText;
  final double iconSize;
  final double fontSize;
  final double largeFontSize;

  const IconSliderTrackShape({
    required this.divisions,
    this.dotColor = Colors.black,
    this.dotSize = DesignSystem.spaceTiny + 0.5,
    this.opacity = 0.2,
    this.leftIcon,
    this.rightIcon,
    this.leftText,
    this.rightText,
    this.iconSize = DesignSystem.widgetIconSizeSmall - 4.0,
    this.fontSize = 10.0, // Keep for visual consistency
    this.largeFontSize = 16.0, // Keep for visual consistency
  });

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 0,
  }) {
    // Get the canvas
    final Canvas canvas = context.canvas;

    // Calculate the track rect
    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // Draw track background
    final Paint trackPaint = Paint()
      ..color =
          sliderTheme.inactiveTrackColor ?? Colors.grey.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final Radius trackRadius = Radius.circular(trackRect.height / 2);

    canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, trackRadius),
      trackPaint,
    );

    // Define icon/text padding (space needed for icons/text at edges)
    const double iconPadding = DesignSystem.spaceL + DesignSystem.spaceTiny;

    // Draw left icon or text
    double leftOffset = trackRect.left;
    if (leftIcon != null) {
      final iconData = leftIcon!;
      final iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(iconData.codePoint),
          style: TextStyle(
            fontSize: iconSize,
            fontFamily: iconData.fontFamily,
            package: iconData.fontPackage,
            color:
                dotColor, // Remove double alpha reduction for better dark mode visibility
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      iconPainter.layout();
      final double iconX = trackRect.left + 8;
      final double iconY = trackRect.center.dy - (iconPainter.height / 2);
      iconPainter.paint(canvas, Offset(iconX, iconY));
      leftOffset = iconX + iconPainter.width + 8;
    } else if (leftText != null) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: leftText,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
            color:
                dotColor, // Remove double alpha reduction for better dark mode visibility
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = trackRect.left + 8;
      final double textY = trackRect.center.dy - (textPainter.height / 2);
      textPainter.paint(canvas, Offset(textX, textY));
      leftOffset = textX + textPainter.width + 8;
    } else {
      leftOffset = trackRect.left + iconPadding;
    }

    // Draw right icon or text
    double rightOffset = trackRect.right;
    if (rightIcon != null) {
      final iconData = rightIcon!;
      final iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(iconData.codePoint),
          style: TextStyle(
            fontSize: iconSize,
            fontFamily: iconData.fontFamily,
            package: iconData.fontPackage,
            color:
                dotColor, // Remove double alpha reduction for better dark mode visibility
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      iconPainter.layout();
      final double iconX = trackRect.right - 8 - iconPainter.width;
      final double iconY = trackRect.center.dy - (iconPainter.height / 2);
      iconPainter.paint(canvas, Offset(iconX, iconY));
      rightOffset = iconX - 8;
    } else if (rightText != null) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: rightText,
          style: TextStyle(
            fontSize: largeFontSize,
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
            color:
                dotColor, // Remove double alpha reduction for better dark mode visibility
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = trackRect.right - 8 - textPainter.width;
      final double textY = trackRect.center.dy - (textPainter.height / 2);
      textPainter.paint(canvas, Offset(textX, textY));
      rightOffset = textX - 8;
    } else {
      rightOffset = trackRect.right - iconPadding;
    }

    // Draw dots along the track within the usable range (between icons/text)
    if (divisions > 0) {
      final Paint dotPaint = Paint()
        ..color = dotColor.withValues(alpha: opacity)
        ..style = PaintingStyle.fill;

      // Calculate usable area width
      final double usableWidth = rightOffset - leftOffset;
      // Calculate step width within the usable area
      final double stepWidth = usableWidth / divisions;

      // Draw dots only within the usable range
      for (int i = 0; i <= divisions; i++) {
        final double x = leftOffset + i * stepWidth;
        final double y = trackRect.center.dy;
        canvas.drawCircle(Offset(x, y), dotSize, dotPaint);
      }
    }
  }
}

enum PageTurn {
  noAnimation,
  slide,
  scroll;

  String getLabel(BuildContext context) {
    switch (this) {
      case PageTurn.noAnimation:
        return L10n.of(context).no_animation;
      case PageTurn.slide:
        return L10n.of(context).slide;
      case PageTurn.scroll:
        return L10n.of(context).scroll;
    }
  }
}

class StyleWidget extends StatefulWidget {
  const StyleWidget({
    super.key,
    required this.themes,
    required this.epubPlayerKey,
    required this.setCurrentPage,
    this.backgroundColor,
    this.textColor,
  });

  final List<ReadTheme> themes;
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function setCurrentPage;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  StyleWidgetState createState() => StyleWidgetState();
}

class StyleWidgetState extends State<StyleWidget> {
  BookStyle bookStyle = Prefs().bookStyle;
  int? currentThemeId = Prefs().readTheme.id;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
        // Top edge shadow only - creates floating appearance while maintaining bottom bar integration
        boxShadow: [
          BoxShadow(
            color: DesignSystem.getStateLayerColor(
              Theme.of(context).colorScheme.shadow,
              0.15,
            ),
            blurRadius:
                DesignSystem.getAdjustedElevation(DesignSystem.elevationM),
            offset: Offset(
              0,
              -DesignSystem.getAdjustedElevation(DesignSystem.elevationM) / 2,
            ),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
        ), // 16.0 (preserves exact spacing)
        child: Column(
          children: [
            // Add consistent top padding using DesignSystem
            const SizedBox(
              height: DesignSystem.spaceL,
            ), // 24.0 (preserves exact spacing)
            sliders(),
            const SizedBox(
              height: DesignSystem.spaceS + 2,
            ), // 10.0 (preserves exact spacing)
            fontSelection(),
            // Add bottom padding for consistent spacing
            const SizedBox(
              height: DesignSystem.spaceL,
            ), // 24.0 (preserves exact spacing)
          ],
        ),
      ),
    );
  }

  List<FontModel> fonts() {
    Directory fontDir = getFontDir();
    List<FontModel> fontList = [
      FontModel(
        label: L10n.of(context).download_fonts,
        name: 'download',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).add_new_font,
        name: 'newFont',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).follow_book,
        name: 'book',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).system_font,
        name: 'system',
        path: 'system',
      ),
    ];
    // fontDir.listSync().forEach((element) {
    //   if (element is File) {
    //     fontList.add(FontModel(
    //       label: getFontNameFromFile(element),
    //       name: 'customFont' + ,
    //       path:
    //           'http://localhost:${Server().port}/fonts/${element.path.split('/').last}',
    //     ));
    //   }
    // });
    // name = 'customFont' + index
    for (int i = 0; i < fontDir.listSync().length; i++) {
      File element = fontDir.listSync()[i] as File;
      fontList.add(
        FontModel(
          label: getFontNameFromFile(element),
          name: 'customFont$i',
          path:
              'http://localhost:${Server().port}/fonts/${element.path.split(Platform.pathSeparator).last}',
        ),
      );
    }

    return fontList;
  }

  Widget fontSelection() {
    FontModel? font = fonts().firstWhere(
      (element) => element.path == Prefs().font.path,
      orElse: () => FontModel(
        label: L10n.of(context).follow_book,
        name: 'book',
        path: '',
      ),
    );

    Widget? leadingIcon(String name) {
      if (name == 'download') {
        return const Icon(
          Icons.download,
          semanticLabel: 'Download fonts',
        );
      } else if (name == 'newFont') {
        return const Icon(
          Icons.add,
          semanticLabel: 'Add new font',
        );
      }
      return null;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: DesignSystem.spaceS,
      ), // 8.0 (preserves exact spacing)
      child: DropdownMenu<FontModel>(
        label: Text(
          L10n.of(context).font,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        textStyle: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
        ),
        menuStyle: MenuStyle(
          backgroundColor: WidgetStateProperty.all(
            widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
          ),
        ),
        initialSelection: font,
        width: MediaQuery.of(context).size.width > 400
            ? 380
            : MediaQuery.of(context).size.width * 0.85,
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              DesignSystem.radiusCircle + 22,
            ), // 50.0 (preserves exact radius)
          ),
        ),
        onSelected: (FontModel? font) async {
          if (font == null) return;
          if (font.name == 'newFont') {
            await importFont();
            setState(() {});
            return;
          } else if (font.name == 'download') {
            Navigator.push(
              context,
              CupertinoPageRoute<void>(
                fullscreenDialog: false,
                builder: (context) => const FontsSettingPage(),
              ),
            );
            return;
          }
          widget.epubPlayerKey.currentState!.changeFont(font);
          Prefs().font = font;
        },
        dropdownMenuEntries: fonts()
            .map(
              (font) => DropdownMenuEntry(
                value: font,
                label: font.label,
                leadingIcon: leadingIcon(font.name),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget sliders() {
    return Padding(
      padding: const EdgeInsets.all(
        DesignSystem.spaceXS - 1,
      ), // 3.0 (preserves exact spacing)
      child: Column(
        children: [
          const SizedBox(
            height: DesignSystem.spaceS + 2,
          ), // 10.0 (preserves exact spacing)
          fontSizeSlider(),
          const SizedBox(
            height: DesignSystem.spaceM - 1,
          ), // 15.0 (preserves exact spacing)
          Row(
            children: [
              Expanded(child: lineHeightSlider()),
              const SizedBox(
                width: DesignSystem.spaceM - 1,
              ), // 15.0 (preserves exact spacing)
              Expanded(child: sideMarginSlider()),
            ],
          ),
        ],
      ),
    );
  }

  Widget sideMarginSlider() {
    return SliderTheme(
      data: SliderThemeData(
        trackHeight: DesignSystem.widgetIconSizeLarge + DesignSystem.spaceS,
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: DesignSystem.spaceM,
          elevation: DesignSystem.elevationS,
        ),
        overlayShape:
            const RoundSliderOverlayShape(overlayRadius: DesignSystem.spaceL),
        trackShape: IconSliderTrackShape(
          divisions: 4,
          dotColor:
              Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          dotSize: DesignSystem.spaceTiny + 0.5,
          opacity: 0.2,
          leftIcon: Icons.margin_rounded,
          rightIcon: Icons.format_align_justify,
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: Colors.black12, // Simplified theme-aware color
        thumbColor: Colors.white,
        overlayColor: Colors.black12, // Simplified theme-aware color
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.sideMargin,
        onChanged: (double value) {
          setState(() {
            bookStyle.sideMargin = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0,
        max: 20,
        divisions: 20,
      ),
    );
  }

  Widget lineHeightSlider() {
    return SliderTheme(
      data: SliderThemeData(
        trackHeight: DesignSystem.widgetIconSizeLarge + DesignSystem.spaceS,
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: DesignSystem.spaceM,
          elevation: DesignSystem.elevationS,
        ),
        overlayShape:
            const RoundSliderOverlayShape(overlayRadius: DesignSystem.spaceL),
        trackShape: IconSliderTrackShape(
          divisions: 4,
          dotColor:
              Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          dotSize: DesignSystem.spaceTiny + 0.5,
          opacity: 0.2,
          leftIcon: Icons.line_weight,
          rightIcon: Icons.height,
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: Colors.black12, // Simplified theme-aware color
        thumbColor: Colors.white,
        overlayColor: Colors.black12, // Simplified theme-aware color
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.lineHeight,
        onChanged: (double value) {
          setState(() {
            bookStyle.lineHeight = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0,
        max: 3,
        divisions: 10,
      ),
    );
  }

  Widget fontSizeSlider() {
    return SliderTheme(
      data: SliderThemeData(
        trackHeight: DesignSystem.widgetIconSizeLarge + DesignSystem.spaceS,
        thumbShape: TextThumbShape(
          thumbRadius: DesignSystem.spaceM,
          textStyle: TextStyle(
            fontSize: 10, // Keep for visual consistency
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
            color: Colors
                .black87, // Keep black for thumb text visibility on white thumb
          ),
          valueToString: (value) => value.toStringAsFixed(1),
        ),
        overlayShape:
            const RoundSliderOverlayShape(overlayRadius: DesignSystem.spaceL),
        trackShape: IconSliderTrackShape(
          divisions: 10,
          dotColor: DesignSystem.getStateLayerColor(
            Theme.of(context).colorScheme.onSurface,
            0.6,
          ),
          dotSize: DesignSystem.spaceTiny + 0.5,
          opacity: 0.2,
          leftText: 'A',
          rightText: 'A',
          fontSize: 10.0, // Keep for visual consistency
          largeFontSize: 16.0, // Keep for visual consistency
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: Colors.black12, // Simplified theme-aware color
        thumbColor: Colors.white,
        overlayColor: Colors.black12, // Simplified theme-aware color
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.fontSize,
        onChanged: (double value) {
          setState(() {
            bookStyle.fontSize = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0.5,
        max: 3.0,
        divisions: 25,
      ),
    );
  }
}

class ThemeChangeWidget extends StatefulWidget {
  const ThemeChangeWidget({
    super.key,
    required this.readTheme,
    required this.setCurrentPage,
  });

  final ReadTheme readTheme;
  final Function setCurrentPage;

  @override
  State<ThemeChangeWidget> createState() => _ThemeChangeWidgetState();
}

class _ThemeChangeWidgetState extends State<ThemeChangeWidget> {
  late ReadTheme readTheme;

  @override
  void initState() {
    super.initState();
    readTheme = widget.readTheme;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SemanticHelpers.button(
          context: context,
          label: 'Change background color',
          hint: 'Opens color picker to customize reading background',
          onTap: () async {
            String? pickingColor =
                await showColorPickerDialog(readTheme.backgroundColor);
            if (pickingColor != '') {
              setState(() {
                readTheme.backgroundColor = pickingColor!;
              });
              updateThemeWithStatusBar(readTheme);
            }
          },
          child: IconButton(
            onPressed: () async {
              String? pickingColor =
                  await showColorPickerDialog(readTheme.backgroundColor);
              if (pickingColor != '') {
                setState(() {
                  readTheme.backgroundColor = pickingColor!;
                });
                updateThemeWithStatusBar(readTheme);
              }
            },
            icon: Icon(
              Icons.circle,
              size: DesignSystem.getAdjustedIconSize(80),
              color: Color(int.parse('0x${readTheme.backgroundColor}')),
            ),
          ),
        ),
        SemanticHelpers.button(
          context: context,
          label: 'Change text color',
          hint: 'Opens color picker to customize reading text color',
          onTap: () async {
            String? pickingColor =
                await showColorPickerDialog(readTheme.textColor);
            if (pickingColor != '') {
              setState(() {
                readTheme.textColor = pickingColor!;
              });
              updateThemeWithStatusBar(readTheme);
            }
          },
          child: IconButton(
            onPressed: () async {
              String? pickingColor =
                  await showColorPickerDialog(readTheme.textColor);
              if (pickingColor != '') {
                setState(() {
                  readTheme.textColor = pickingColor!;
                });
                updateThemeWithStatusBar(readTheme);
              }
            },
            icon: Icon(
              Icons.text_fields,
              size: DesignSystem.getAdjustedIconSize(60),
              color: Color(int.parse('0x${readTheme.textColor}')),
            ),
          ),
        ),
        const Expanded(
          child: SizedBox(),
        ),
        SemanticHelpers.button(
          context: context,
          label: 'Delete theme',
          hint: 'Removes this custom reading theme permanently',
          onTap: () {
            deleteTheme(readTheme.id!);
            widget.setCurrentPage(const SizedBox(height: 1));
          },
          child: IconButton(
            onPressed: () {
              deleteTheme(readTheme.id!);
              widget.setCurrentPage(const SizedBox(height: 1));
              // setState(() {});
            },
            icon: Icon(
              Icons.delete,
              size: DesignSystem.getAdjustedIconSize(40),
            ),
          ),
        ),
      ],
    );
  }

  /// Enhanced updateTheme that also updates status bar styling
  ///
  /// This method updates the theme in the database and also applies
  /// the new theme colors to the status bar for consistent appearance.
  void updateThemeWithStatusBar(ReadTheme readTheme) {
    // Validate contrast before saving
    final contrastResult = ContrastAudit.validateReadingTheme(
      readTheme.backgroundColor,
      readTheme.textColor,
    );

    if (!contrastResult.isValidAA) {
      // Show warning toast for poor contrast
      AnxToast.showWarning(
        'Poor contrast ratio: ${contrastResult.ratio.toStringAsFixed(1)}:1. Recommended minimum: 4.5:1',
        duration: 4000,
      );
    }

    // Update theme in database
    updateTheme(readTheme);
  }

  Future<String?> showColorPickerDialog(String currColor) async {
    Color pickedColor = Color(int.parse('0x$currColor'));

    await showDialog<void>(
      context: navigatorKey.currentState!.overlay!.context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: ColorPicker(
              hexInputBar: true,
              pickerColor: pickedColor,
              onColorChanged: (Color color) {
                pickedColor = color;
              },
              pickerAreaHeightPercent: 0.8,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context)
                    .pop(pickedColor.toARGB32().toRadixString(16));
              },
            ),
          ],
        );
      },
    );

    return pickedColor.toARGB32().toRadixString(16);
  }
}
