import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/service/tts/base_tts.dart';
import 'package:dasso_reader/service/tts/tts_handler.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/reading_page/widget_title.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'dart:async';

class TtsWidget extends StatefulWidget {
  const TtsWidget({
    super.key,
    required this.epubPlayerKey,
    this.backgroundColor,
    this.textColor,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  State<TtsWidget> createState() => _TtsWidgetState();
}

class _TtsWidgetState extends State<TtsWidget> {
  double volume = TtsHandler().volume;
  double pitch = TtsHandler().pitch;
  double rate = TtsHandler().rate;
  double stopSeconds = 0;
  Timer? stopTimer;

  @override
  void initState() {
    if (TtsHandler().ttsStateNotifier.value != TtsStateEnum.playing) {
      TtsHandler()
          .init(
        widget.epubPlayerKey.currentState!.initTts,
        widget.epubPlayerKey.currentState!.ttsNext,
        widget.epubPlayerKey.currentState!.ttsPrev,
      )
          .then((value) {
        audioHandler.play();
      });
    }

    super.initState();
  }

  @override
  void dispose() {
    // Cancel timer to prevent memory leaks
    stopTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TtsStateEnum>(
      valueListenable: TtsHandler().ttsStateNotifier,
      builder: (context, ttsState, child) {
        Widget volume() {
          return Row(
            children: [
              Text(
                L10n.of(context).tts_volume,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Expanded(
                child: SemanticHelpers.slider(
                  label: 'Volume',
                  value: TtsHandler().volume,
                  min: 0.0,
                  max: 1.0,
                  unit: '%',
                  child: Slider(
                    value: TtsHandler().volume,
                    onChanged: (newVolume) {
                      setState(() {
                        TtsHandler().volume = newVolume;
                      });
                    },
                    min: 0.0,
                    max: 1.0,
                    divisions: 10,
                    label: TtsHandler().volume.toStringAsFixed(1),
                  ),
                ),
              ),
            ],
          );
        }

        Widget pitch() {
          return Row(
            children: [
              Text(
                L10n.of(context).tts_pitch,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Expanded(
                child: SemanticHelpers.slider(
                  label: 'Pitch',
                  value: TtsHandler().pitch,
                  min: 0.5,
                  max: 2.0,
                  child: Slider(
                    value: TtsHandler().pitch,
                    onChanged: (newPitch) {
                      setState(() {
                        TtsHandler().pitch = newPitch;
                      });
                    },
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    label: TtsHandler().pitch.toStringAsFixed(1),
                  ),
                ),
              ),
            ],
          );
        }

        Widget rate() {
          return Row(
            children: [
              Text(
                L10n.of(context).tts_rate,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Expanded(
                child: SemanticHelpers.slider(
                  label: 'Rate',
                  value: TtsHandler().rate,
                  min: 0.0,
                  max: 2.0,
                  child: Slider(
                    value: TtsHandler().rate,
                    onChanged: (newRate) {
                      setState(() {
                        TtsHandler().rate = newRate;
                      });
                    },
                    min: 0.0,
                    max: 2.0,
                    divisions: 10,
                    label: TtsHandler().rate.toStringAsFixed(1),
                  ),
                ),
              ),
            ],
          );
        }

        Widget sliders() {
          return Padding(
            padding: const EdgeInsets.fromLTRB(
              DesignSystem.spaceM + 4,
              DesignSystem.spaceXS + 1,
              DesignSystem.spaceM + 4,
              0,
            ), // 20, 5, 20, 0 (preserves exact spacing)
            child: Column(
              children: [
                volume(),
                pitch(),
                rate(),
                // TTS Type toggle removed as it's already available in the more settings
              ],
            ),
          );
        }

        Widget buttons() {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SemanticHelpers.button(
                context: context,
                label: 'Previous section',
                hint: 'Jump to the previous section and start narration',
                onTap: () async {
                  audioHandler.stop();
                  await widget.epubPlayerKey.currentState!.ttsPrevSection();
                  TtsHandler().playPrevious();
                },
                child: IconButton(
                  onPressed: () async {
                    audioHandler.stop();
                    await widget.epubPlayerKey.currentState!.ttsPrevSection();
                    TtsHandler().playPrevious();
                  },
                  icon: const Icon(EvaIcons.arrowhead_left),
                ),
              ),
              SemanticHelpers.button(
                context: context,
                label: 'Previous sentence',
                hint: 'Go back to the previous sentence in narration',
                onTap: () {
                  TtsHandler().playPrevious();
                },
                child: IconButton(
                  onPressed: () {
                    TtsHandler().playPrevious();
                  },
                  icon: const Icon(EvaIcons.chevron_left),
                ),
              ),
              SemanticHelpers.button(
                context: context,
                label: ttsState == TtsStateEnum.playing
                    ? 'Pause narration'
                    : 'Play narration',
                hint: ttsState == TtsStateEnum.playing
                    ? 'Pauses the text-to-speech narration'
                    : 'Starts or resumes text-to-speech narration',
                onTap: () async {
                  ttsState == TtsStateEnum.playing
                      ? audioHandler.pause()
                      : audioHandler.play();
                },
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minWidth: 44.0,
                    minHeight: 44.0,
                  ),
                  child: IconButton(
                    onPressed: () async {
                      // Tts.toggle();
                      ttsState == TtsStateEnum.playing
                          ? audioHandler.pause()
                          : audioHandler.play();
                    },
                    icon: ttsState == TtsStateEnum.playing
                        ? const Icon(EvaIcons.pause_circle_outline)
                        : const Icon(EvaIcons.play_circle_outline),
                  ),
                ),
              ),
              SemanticHelpers.button(
                context: context,
                label: 'Stop narration',
                hint: 'Stops the text-to-speech narration completely',
                onTap: () {
                  audioHandler.stop();
                },
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minWidth: 44.0,
                    minHeight: 44.0,
                  ),
                  child: IconButton(
                    onPressed: () {
                      audioHandler.stop();
                    },
                    icon: const Icon(EvaIcons.stop_circle_outline),
                  ),
                ),
              ),
              SemanticHelpers.button(
                context: context,
                label: 'Next sentence',
                hint: 'Skip to the next sentence in narration',
                onTap: () {
                  TtsHandler().playNext();
                },
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minWidth: 44.0,
                    minHeight: 44.0,
                  ),
                  child: IconButton(
                    onPressed: () {
                      TtsHandler().playNext();
                    },
                    icon: const Icon(EvaIcons.chevron_right),
                  ),
                ),
              ),
              SemanticHelpers.button(
                context: context,
                label: 'Next section',
                hint: 'Jump to the next section and start narration',
                onTap: () async {
                  audioHandler.stop();
                  await widget.epubPlayerKey.currentState!.ttsNextSection();
                  TtsHandler().playNext();
                },
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minWidth: 44.0,
                    minHeight: 44.0,
                  ),
                  child: IconButton(
                    onPressed: () async {
                      audioHandler.stop();
                      await widget.epubPlayerKey.currentState!.ttsNextSection();
                      TtsHandler().playNext();
                    },
                    icon: const Icon(EvaIcons.arrowhead_right),
                  ),
                ),
              ),
            ],
          );
        }

        Widget stopTimerWidget() {
          return Padding(
            padding: const EdgeInsets.fromLTRB(
              DesignSystem.spaceM + 4,
              DesignSystem.spaceXS + 1,
              DesignSystem.spaceM + 4,
              0,
            ), // 20, 5, 20, 0 (preserves exact spacing)
            child: Row(
              children: [
                const Icon(
                  EvaIcons.clock_outline,
                  semanticLabel: 'Timer',
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Slider(
                    value: stopSeconds / 60,
                    onChanged: (newValue) {
                      setState(() {
                        stopSeconds = newValue * 60;
                        stopTimer?.cancel();

                        if (stopSeconds > 0) {
                          stopTimer = Timer.periodic(
                            const Duration(seconds: 5),
                            (timer) {
                              if (stopSeconds > 5) {
                                stopSeconds -= 5;
                                if (mounted) {
                                  setState(() {});
                                }
                                return;
                              } else {
                                TtsHandler().stop();
                                stopSeconds = 0;
                                timer.cancel();
                                if (mounted) {
                                  setState(() {});
                                }
                              }
                            },
                          );
                        }
                      });
                    },
                    min: 0.0,
                    max: 60.0,
                    label: L10n.of(context)
                        .common_minutes_full((stopSeconds / 60).round()),
                  ),
                ),
                Text(
                  L10n.of(context).tts_stop_after((stopSeconds / 60).ceil()),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            color:
                widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
            // Top edge shadow only - creates floating appearance while maintaining bottom bar integration
            boxShadow: [
              BoxShadow(
                color: DesignSystem.getStateLayerColor(
                  Theme.of(context).colorScheme.shadow,
                  0.15,
                ),
                blurRadius:
                    DesignSystem.getAdjustedElevation(DesignSystem.elevationM),
                offset: Offset(
                  0,
                  -DesignSystem.getAdjustedElevation(DesignSystem.elevationM) /
                      2,
                ),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceM,
            ), // 16.0 (preserves exact spacing)
            child: Column(
              children: [
                // Add consistent top padding for spacing
                const SizedBox(
                  height: DesignSystem.spaceL,
                ), // 24.0 (preserves exact spacing)
                widgetTitle(
                  L10n.of(context).tts_narrator,
                  ReadingSettings.style,
                ),
                buttons(),
                const Divider(),
                stopTimerWidget(),
                sliders(),
                // Add bottom padding for consistent spacing
                const SizedBox(
                  height: DesignSystem.spaceL,
                ), // 24.0 (preserves exact spacing)
              ],
            ),
          ),
        );
      },
    );
  }
}
