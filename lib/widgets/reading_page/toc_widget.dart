import 'dart:math';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/search_result_model.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/models/toc_item.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/widgets/reading_page/notes_widget.dart';
import 'package:dasso_reader/widgets/reading_page/widgets/bookmark.dart';
import 'package:flutter/material.dart';

class TocWidget extends StatefulWidget {
  final List<TocItem> tocItems;
  final Function hideAppBarAndBottomBar;
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Book book;
  final Color? backgroundColor;
  final Color? textColor;

  const TocWidget({
    super.key,
    required this.tocItems,
    required this.hideAppBarAndBottomBar,
    required this.epubPlayerKey,
    required this.book,
    this.backgroundColor,
    this.textColor,
  });

  @override
  State<TocWidget> createState() => _TocWidgetState();
}

class _TocWidgetState extends State<TocWidget> with TickerProviderStateMixin {
  ScrollController listViewController = ScrollController();
  List<bool> isExpanded = [];
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    for (var item in widget.tocItems) {
      isExpanded.add(_isSelected(item));
    }

    final offset = isExpanded.indexWhere((isExpanded) => isExpanded);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (listViewController.hasClients) {
        listViewController.jumpTo(
          min(offset * 48, listViewController.position.maxScrollExtent - 48),
        );
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    listViewController
        .dispose(); // Dispose ScrollController to prevent memory leaks
    super.dispose();
  }

  bool _isSelected(TocItem tocItem) {
    if (tocItem.href == widget.epubPlayerKey.currentState!.chapterHref) {
      return true;
    }
    for (var subItem in tocItem.subitems) {
      if (_isSelected(subItem)) {
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    // TOC content widget without search box
    Widget tocContent = ListView.builder(
      controller: listViewController,
      itemCount: widget.tocItems.length,
      itemBuilder: (context, index) {
        return TocItemWidget(
          tocItem: widget.tocItems[index],
          hideAppBarAndBottomBar: widget.hideAppBarAndBottomBar,
          epubPlayerKey: widget.epubPlayerKey,
        );
      },
    );

    return SizedBox(
      height: 0.6 * MediaQuery.of(context).size.height,
      child: Container(
        decoration: BoxDecoration(
          color:
              widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
          // Top edge shadow only - creates floating appearance while maintaining bottom bar integration
          boxShadow: [
            BoxShadow(
              color: DesignSystem.getStateLayerColor(
                Theme.of(context).colorScheme.shadow,
                0.15,
              ),
              blurRadius:
                  DesignSystem.getAdjustedElevation(DesignSystem.elevationM),
              offset: Offset(
                0,
                -DesignSystem.getAdjustedElevation(DesignSystem.elevationM) / 2,
              ),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM,
          ), // 16.0 (preserves exact spacing)
          child: Column(
            children: [
              // Add consistent top padding for spacing
              const SizedBox(
                height: DesignSystem.spaceL,
              ), // 24.0 (preserves exact spacing)
              // Tab bar - use Flutter's automatic theme colors like anx-reader
              TabBar(
                controller: _tabController,
                tabs: [
                  Tab(text: L10n.of(context).reading_contents),
                  Tab(text: L10n.of(context).navBar_notes),
                  Tab(text: L10n.of(context).reading_bookmark),
                ],
              ),

              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // TOC Content tab
                    tocContent,

                    // Notes tab - reusing existing ReadingNotes widget
                    ReadingNotes(
                      book: widget.book,
                      backgroundColor: widget.backgroundColor,
                      textColor: widget.textColor,
                    ),

                    // Bookmark tab - new bookmark widget
                    BookmarkWidget(epubPlayerKey: widget.epubPlayerKey),
                  ],
                ),
              ),
              // Add bottom padding for consistent spacing
              const SizedBox(
                height: DesignSystem.spaceL,
              ), // 24.0 (preserves exact spacing)
            ],
          ),
        ),
      ),
    );
  }
}

Widget searchResultWidget({
  required SearchResultModel searchResult,
  required Function hideAppBarAndBottomBar,
  required GlobalKey<EpubPlayerState> epubPlayerKey,
}) {
  bool isExpanded = true;
  TextStyle matchStyle = TextStyle(
    color: Theme.of(navigatorKey.currentContext!).colorScheme.primary,
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
  );
  TextStyle prePostStyle = const TextStyle(
    color: Colors.black54, // Use theme-aware color
  );
  return StatefulBuilder(
    builder: (context, setState) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextButton(
            onPressed: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    searchResult.label,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                isExpanded
                    ? const Icon(Icons.expand_less)
                    : const Icon(Icons.expand_more),
                // const Spacer(),
                Text(
                  searchResult.subitems.length.toString(),
                  style: const TextStyle(
                    color: Colors.black54,
                  ), // Use theme-aware color
                ),
              ],
            ),
          ),
          if (isExpanded)
            for (var subItem in searchResult.subitems)
              TextButton(
                onPressed: () {
                  hideAppBarAndBottomBar(false);
                  epubPlayerKey.currentState!.goToCfi(subItem.cfi);
                },
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(text: subItem.pre, style: prePostStyle),
                      TextSpan(text: subItem.match, style: matchStyle),
                      TextSpan(text: subItem.post, style: prePostStyle),
                    ],
                  ),
                ),
              ),
        ],
      );
    },
  );
}

class TocItemWidget extends StatefulWidget {
  final TocItem tocItem;
  final Function hideAppBarAndBottomBar;
  final GlobalKey<EpubPlayerState> epubPlayerKey;

  const TocItemWidget({
    super.key,
    required this.tocItem,
    required this.hideAppBarAndBottomBar,
    required this.epubPlayerKey,
  });

  @override
  TocItemWidgetState createState() => TocItemWidgetState();
}

class TocItemWidgetState extends State<TocItemWidget> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = _isSelected(widget.tocItem);
  }

  TextStyle tocStyle() => TextStyle(
        fontSize: DesignSystem.getAdjustedFontSize(
          context,
          DesignSystem.fontSizeM,
        ),
        color: Theme.of(context).colorScheme.onSurface,
      );

  TextStyle tocStyleSelected() => TextStyle(
        fontSize: DesignSystem.getAdjustedFontSize(
          context,
          DesignSystem.fontSizeM,
        ),
        color: Theme.of(context).colorScheme.primary,
        fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
      );

  bool _isSelected(TocItem tocItem) {
    if (tocItem.href == widget.epubPlayerKey.currentState!.chapterHref) {
      return true;
    }
    for (var subItem in tocItem.subitems) {
      if (_isSelected(subItem)) {
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: SemanticHelpers.button(
                context: context,
                label: 'Chapter: ${widget.tocItem.label.trim()}',
                hint: 'Navigate to this chapter in the book',
                onTap: () {
                  widget.hideAppBarAndBottomBar(false);
                  widget.epubPlayerKey.currentState!
                      .goToHref(widget.tocItem.href);
                },
                child: TextButton(
                  onPressed: () {
                    widget.hideAppBarAndBottomBar(false);
                    widget.epubPlayerKey.currentState!
                        .goToHref(widget.tocItem.href);
                  },
                  style: const ButtonStyle(
                    alignment: Alignment.centerLeft,
                  ),
                  child: Text(
                    widget.tocItem.label.trim(),
                    style: _isSelected(widget.tocItem)
                        ? tocStyleSelected()
                        : tocStyle(),
                  ),
                ),
              ),
            ),
            if (widget.tocItem.subitems.isNotEmpty)
              SemanticHelpers.button(
                context: context,
                label: _isExpanded ? 'Collapse chapter' : 'Expand chapter',
                hint: _isExpanded ? 'Hide sub-chapters' : 'Show sub-chapters',
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: IconButton(
                  icon:
                      Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
              ),
          ],
        ),
        if (_isExpanded)
          for (var subItem in widget.tocItem.subitems)
            Padding(
              padding: const EdgeInsets.only(
                left: DesignSystem.spaceL + DesignSystem.spaceTiny,
              ),
              child: TocItemWidget(
                tocItem: subItem,
                hideAppBarAndBottomBar: widget.hideAppBarAndBottomBar,
                epubPlayerKey: widget.epubPlayerKey,
              ),
            ),
        const Divider(
          indent: 10,
          endIndent: 20,
          thickness: 1,
          color: Colors.black26,
        ), // Use theme-aware color
      ],
    );
  }
}
